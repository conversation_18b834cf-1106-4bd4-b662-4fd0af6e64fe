# 光纤测试记录生成脚本

## 功能说明

本脚本用于根据光配设备信息自动生成空余纤芯的OTDR测试记录，符合电力通信纤芯管理要求。

## 主要特点

- ✅ **智能识别空余纤芯**：自动分析去向为空或未到使用年限的纤芯
- ✅ **符合行业标准**：平均损耗控制在0.19-0.21dB/km
- ✅ **工作日测试**：自动选择6月和11月的工作日进行测试
- ✅ **异常点检测**：15%概率包含异常点位置和衰耗值
- ✅ **完整数据验证**：自动验证生成结果的正确性

## 使用方法

### 1. 环境要求

```bash
pip install pandas openpyxl numpy
```

### 2. 文件准备

将原始Excel文件命名为 `xx.xlsx` 并放在脚本同目录下。

### 3. 运行脚本

```bash
python3 generate_fiber_test_records.py
```

### 4. 输出文件

生成 `xx_spare_fibers_generated.xlsx` 文件，包含：
- 1个Sheet1（原始光配设备信息）
- 34个测试记录工作表（2009-2025年，每年2次）

## 业务逻辑

### 空余纤芯定义

1. **去向为空的纤芯**：一直为空余状态
2. **未到使用年限的纤芯**：在开始使用年限之前为空余状态

### 测试记录规则

- **测试对象**：只对空余纤芯进行OTDR测试
- **已使用纤芯**：无测试数据（无法断开连接测试）
- **测试频次**：每年2次（春季6月、秋季11月）
- **测试日期**：自动选择工作日

### 数据标准

- **线路长度**：48km
- **平均损耗**：0.19-0.21dB/km
- **全程总损耗**：平均损耗×线路长度+接头损耗
- **异常点格式**：位置km/衰耗dB（如：15.563km/0.545dB）

## 输出示例

### 纤芯状态分析
```
配线盘611: 一直空余（去向为空）
配线盘612: 一直空余（去向为空）
配线盘613-620: 2009年开始使用（无测试数据）
配线盘621: 2009-2015年空余，2016年开始使用
配线盘622: 2009-2015年空余，2016年开始使用
```

### 测试记录分布
- **2009-2015年**：4个空余纤芯（611、612、621、622）
- **2016-2025年**：2个空余纤芯（611、612）

### 工作表命名
```
测试时间1（2025年11月8日）
测试时间2（2025年6月10日）
测试时间3（2024年11月9日）
...
测试时间34（2009年6月9日）
```

## 数据验证

脚本会自动验证：
- ✅ 空余纤芯数量正确性
- ✅ 平均损耗范围符合标准
- ✅ 测试日期为工作日
- ✅ 异常点数据格式正确

## 注意事项

1. **输入文件格式**：确保xx.xlsx包含正确的Sheet1结构
2. **列名要求**：光配名称、配线盘、去向、投运年限
3. **数据完整性**：确保配线盘和投运年限字段有效
4. **文件权限**：确保脚本有读写权限

## 技术支持

如遇问题，请检查：
1. 输入文件是否存在且格式正确
2. Python环境是否安装必要依赖
3. 文件读写权限是否正常

---

**版本**：1.0  
**更新日期**：2025年  
**适用场景**：电力通信光纤测试记录管理
